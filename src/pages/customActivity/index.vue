<template>
    <div>
        <el-card shadow="hover" :body-style="{ padding: '20px' }">
            <el-form
                :model="queryForm"
                ref="queryForm"
                label-width="80px"
                :inline="true"
                @submit.native.prevent
                size="mini"
            >
                <el-form-item>
                    <el-input
                        v-model="queryForm.activity_name"
                        placeholder="活动名称"
                        @keyup.enter.native="queryActivities"
                        clearable
                    ></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="queryActivities"
                        >查询</el-button
                    >
                    <el-button type="success" @click="openDialog()"
                        >新增活动</el-button
                    >
                </el-form-item>
            </el-form>
        </el-card>

        <el-card
            shadow="hover"
            style="margin-top: 10px"
            :body-style="{ padding: '20px' }"
        >
            <el-table
                :data="tableData"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                style="width: 100%"
            >
                <el-table-column label="活动ID" prop="id" width="100">
                </el-table-column>
                <el-table-column
                    label="活动名称"
                    prop="activity_name"
                    width="150"
                >
                </el-table-column>

                <el-table-column
                    label="自选价格"
                    prop="custom_price"
                    width="120"
                >
                    <template slot-scope="scope">
                        ¥{{ scope.row.custom_price }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="自选数量"
                    prop="custom_quantity"
                    width="120"
                >
                </el-table-column>
                <el-table-column label="商品排序" prop="sort_rule" width="150">
                    <template slot-scope="scope">
                        {{ getSortLabel(scope.row.sort_rule) }}
                    </template>
                </el-table-column>
                <el-table-column label="活动状态" prop="status" width="100">
                    <template slot-scope="scope">
                        <el-tag
                            :type="scope.row.status === 0 ? 'info' : 'success'"
                        >
                            {{ scope.row.status === 0 ? "隐藏" : "开启" }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="排序值" prop="sort_value" width="100">
                </el-table-column>
                <el-table-column
                    label="创建时间"
                    prop="create_time"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    label="分享主标题"
                    prop="main_title"
                    width="150"
                >
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="200">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            size="mini"
                            @click="openDialog(scope.row)"
                            >编辑</el-button
                        >
                        <el-button
                            type="text"
                            size="mini"
                            @click="openProductListDialog(scope.row)"
                            >商品列表</el-button
                        >
                        <el-popover
                            placement="left-start"
                            width="200"
                            trigger="click"
                            @show="miniProgramCode(scope.row.id)"
                        >
                            <img :src="minipogramCode" style="width: 180px" />
                            <el-button
                                slot="reference"
                                type="text"
                                size="mini"
                                style="margin-left: 8px"
                                >小程序二维码</el-button
                            >
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <div style="text-align: center">
            <el-pagination
                background
                style="margin-top: 10px"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-size="queryForm.limit"
                :current-page="queryForm.page"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 新增/编辑弹窗 -->
        <el-dialog
            :close-on-click-modal="false"
            :title="isEdit ? '编辑活动' : '新增活动'"
            :visible.sync="dialogVisible"
            width="800px"
            @close="closeDialog"
        >
            <el-form
                ref="activityForm"
                :model="formData"
                :rules="formRules"
                label-width="120px"
            >
                <el-form-item label="活动名称" prop="activity_name">
                    <el-input
                        v-model="formData.activity_name"
                        placeholder="请输入活动名称"
                        clearable
                        style="width: 300px"
                    ></el-input>
                </el-form-item>

                <el-form-item label="自选价格" prop="custom_price">
                    <el-input-number
                        :disabled="isEdit"
                        v-model="formData.custom_price"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        placeholder="请输入自选价格"
                        style="width: 200px"
                    ></el-input-number>
                </el-form-item>

                <el-form-item label="自选数量" prop="custom_quantity">
                    <el-input-number
                        :disabled="isEdit"
                        v-model="formData.custom_quantity"
                        :min="1"
                        :step="1"
                        placeholder="请输入自选数量"
                        style="width: 150px"
                    ></el-input-number>
                </el-form-item>

                <el-form-item label="商品排序" prop="sort_rule">
                    <el-radio-group v-model="formData.sort_rule">
                        <el-radio :label="0">添加时间倒序</el-radio>
                        <el-radio :label="1">上架时间倒序</el-radio>
                        <el-radio :label="2">闪购排序倒序</el-radio>
                        <el-radio :label="3">随机</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="活动状态" prop="status">
                    <el-radio-group v-model="formData.status">
                        <el-radio :label="1">开启</el-radio>
                        <el-radio :label="0">隐藏</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="活动排序值" prop="sort_value">
                    <el-input-number
                        v-model="formData.sort_value"
                        :min="0"
                        :step="1"
                        placeholder="请输入排序值"
                        style="width: 150px"
                    ></el-input-number>
                </el-form-item>

                <el-form-item label="头图" prop="header_image">
                    <vos-oss
                        v-if="dialogVisible"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="headerImageArr"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <el-form-item label="展示图" prop="image">
                    <vos-oss
                        v-if="dialogVisible"
                        list-type="picture-card"
                        :showFileList="true"
                        :limit="1"
                        :dir="dir"
                        :file-list="displayImageArr"
                        :limitWhList="[750, 750]"
                    >
                        <i slot="default" class="el-icon-plus"></i>
                    </vos-oss>
                </el-form-item>

                <!-- 分享配置 -->
                <el-card shadow="hover" style="margin-top: 20px">
                    <div slot="header">
                        <span>分享配置</span>
                    </div>

                    <el-form-item label="分享图" prop="share_image">
                        <vos-oss
                            v-if="dialogVisible"
                            list-type="picture-card"
                            :showFileList="true"
                            :limit="1"
                            :dir="dir"
                            :file-list="shareImageArr"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>

                    <el-form-item label="分享主标题" prop="main_title">
                        <el-input
                            v-model="formData.main_title"
                            placeholder="请输入分享主标题"
                            clearable
                            style="width: 300px"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="分享副标题" prop="sub_title">
                        <el-input
                            v-model="formData.sub_title"
                            placeholder="请输入分享副标题"
                            clearable
                            style="width: 300px"
                        ></el-input>
                    </el-form-item>
                </el-card>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
        </el-dialog>

        <!-- 商品列表弹窗 -->
        <el-dialog
            :close-on-click-modal="false"
            title="商品列表"
            :visible.sync="productListDialogVisible"
            width="900px"
            @close="closeProductListDialog"
        >
            <div style="margin-bottom: 20px">
                <el-button type="primary" @click="openAddProductDialog"
                    >新增商品</el-button
                >
            </div>

            <el-table
                :data="productList"
                border
                size="small"
                :header-cell-style="{ 'text-align': 'center' }"
                :cell-style="{ 'text-align': 'center' }"
                style="width: 100%"
            >
                <el-table-column
                    label="商品名称"
                    prop="goods_name"
                    min-width="200"
                >
                </el-table-column>
                <el-table-column label="商品期数" prop="period" width="120">
                </el-table-column>
                <el-table-column
                    label="套餐名称"
                    prop="package_name"
                    width="150"
                >
                </el-table-column>
                <el-table-column
                    label="套餐价格"
                    prop="package_price"
                    width="120"
                >
                    <template slot-scope="scope">
                        ¥{{ scope.row.package_price }}
                    </template>
                </el-table-column>
                <!-- <el-table-column
                    label="商品文案"
                    prop="product_copy"
                    width="200"
                >
                </el-table-column> -->
                <el-table-column
                    label="创建时间"
                    prop="create_time"
                    width="180"
                >
                </el-table-column>
            </el-table>

            <span slot="footer" class="dialog-footer">
                <el-button @click="productListDialogVisible = false"
                    >关闭</el-button
                >
            </span>
        </el-dialog>

        <!-- 新增商品弹窗 -->
        <el-dialog
            :close-on-click-modal="false"
            title="新增商品"
            :visible.sync="addProductDialogVisible"
            width="1000px"
            @close="closeAddProductDialog"
        >
            <el-form
                ref="productForm"
                :model="productFormData"
                :rules="productFormRules"
                label-width="120px"
            >
                <el-form-item label="商品期数" prop="period">
                    <el-input
                        v-model="productFormData.period"
                        placeholder="请输入商品期数"
                        style="width: 200px; margin-right: 10px"
                    ></el-input>
                    <el-button
                        type="primary"
                        @click="queryPackages"
                        :disabled="!productFormData.period"
                        >查询套餐</el-button
                    >
                </el-form-item>

                <el-form-item label="商品名称" prop="goods_name">
                    <el-input
                        v-model="productFormData.goods_name"
                        placeholder="请输入商品名称"
                        clearable
                        style="width: 300px"
                    ></el-input>
                </el-form-item>

                <!-- 隐藏商品频道选择 -->
                <!-- <el-form-item label="商品频道" prop="periods_type">
                    <el-select
                        v-model="productFormData.periods_type"
                        placeholder="请选择商品频道"
                        style="width: 200px"
                    >
                        <el-option :value="0" label="频道0"></el-option>
                        <el-option :value="1" label="频道1"></el-option>
                        <el-option :value="2" label="频道2"></el-option>
                    </el-select>
                </el-form-item> -->

                <el-form-item
                    label="套餐选择"
                    prop="package_id"
                    v-if="packageOptions.length > 0"
                >
                    <el-select
                        v-model="productFormData.package_id"
                        placeholder="请选择套餐"
                        style="width: 300px"
                        @change="onPackageChange"
                    >
                        <el-option
                            v-for="item in packageOptions"
                            :key="item.id"
                            :label="`${item.package_name} - ¥${item.price}`"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="商品文案" prop="product_copy">
                    <Tinymce
                        ref="productCopyEditor"
                        v-model="productFormData.product_copy"
                        :height="300"
                        placeholder="请输入商品文案"
                    />
                </el-form-item>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button @click="addProductDialogVisible = false"
                    >取消</el-button
                >
                <el-button type="primary" @click="submitProductForm"
                    >确定</el-button
                >
            </span>
        </el-dialog>
    </div>
</template>

<script>
import VosOss from "vos-oss";
import Tinymce from "@/components/Tinymce";

export default {
    name: "CustomActivity",
    components: {
        VosOss,
        Tinymce,
    },
    data() {
        return {
            queryForm: {
                page: 1,
                limit: 10,
                activity_name: "",
            },
            tableData: [],
            minipogramCode: "",
            total: 0,
            packageInfo: {},
            dialogVisible: false,
            isEdit: false,
            dir: "vinehoo/vos/marketing/",
            headerImageArr: [],
            displayImageArr: [],
            shareImageArr: [],
            // 商品列表相关
            productListDialogVisible: false,
            currentActivity: null,
            productList: [],
            // 新增商品相关
            addProductDialogVisible: false,
            productImageArr: [],
            productFormData: {
                id: null,
                activity_id: null,
                period: null,
                periods_type: 0,
                package_id: null,
                goods_name: "",
                product_copy: "",
            },
            formData: {
                id: null,
                activity_name: "",
                main_title: "",
                sub_title: "",
                custom_price: 0,
                custom_quantity: 1,
                sort_rule: 0,
                status: 1,
                sort_value: 0,
                header_image: "",
                image: "",
                share_image: "",
            },
            formRules: {
                activity_name: [
                    {
                        required: true,
                        message: "请输入活动名称",
                        trigger: "blur",
                    },
                ],
                custom_price: [
                    {
                        required: true,
                        message: "请输入自选价格",
                        trigger: "blur",
                    },
                ],
                custom_quantity: [
                    {
                        required: true,
                        message: "请输入自选数量",
                        trigger: "blur",
                    },
                ],
                sort_rule: [
                    {
                        required: true,
                        message: "请选择商品排序",
                        trigger: "change",
                    },
                ],
                status: [
                    {
                        required: true,
                        message: "请选择活动状态",
                        trigger: "change",
                    },
                ],
                sort_value: [
                    {
                        required: true,
                        message: "请输入排序值",
                        trigger: "blur",
                    },
                ],
                // header_image: [
                //     { required: true, message: "请上传头图", trigger: "blur" }
                // ],
                // share_image: [
                //     { required: true, message: "请上传分享图", trigger: "blur" }
                // ],
                main_title: [
                    {
                        required: true,
                        message: "请输入分享主标题",
                        trigger: "blur",
                    },
                ],
                sub_title: [
                    {
                        required: true,
                        message: "请输入分享副标题",
                        trigger: "blur",
                    },
                ],
            },
            // 商品表单验证规则
            productFormRules: {
                goods_name: [
                    {
                        required: true,
                        message: "请输入商品名称",
                        trigger: "blur",
                    },
                ],
                period: [
                    {
                        required: true,
                        message: "请输入商品期数",
                        trigger: "blur",
                    },
                ],
                // 移除 periods_type 的验证规则，因为现在是自动获取的
                package_id: [
                    {
                        required: true,
                        message: "请选择套餐",
                        trigger: "change",
                    },
                ],
                product_copy: [
                    {
                        required: true,
                        message: "请输入商品文案",
                        trigger: "blur",
                    },
                ],
            },
            packageOptions: [], // 套餐选项
            selectedPackage: null, // 选中的套餐信息
        };
    },

    mounted() {
        this.getActivityList();
    },

    methods: {
        // 获取活动列表
        getActivityList() {
            this.$request.customActivity
                .getCustomActivityList(this.queryForm)
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.tableData = result.data.data.list.map((item) => ({
                            id: item.id,
                            activity_name: item.activity_name,
                            main_title: item.main_title,
                            sub_title: item.sub_title,
                            custom_price: item.custom_price,
                            custom_quantity: item.custom_quantity,
                            sort_rule: item.sort_rule,
                            status: item.status,
                            sort_value: item.sort_value,
                            header_image: item.header_image,
                            image: item.image,
                            share_image: item.share_image,
                            create_time: item.create_time,
                            update_time: item.update_time,
                        }));
                        this.total = result.data.data.total;
                    }
                })
                .catch((error) => {
                    console.error("获取活动列表失败:", error);
                    this.$message.error("获取活动列表失败");
                });
        },

        // 查询活动
        queryActivities() {
            this.queryForm.page = 1;
            this.getActivityList();
        },

        async miniProgramCode(id) {
            this.minipogramCode = "";
            const res = await this.$request.customActivity.miniProgramCode({
                activity_id: id,
            });
            if (res.data.error_code === 0) {
                this.minipogramCode =
                    "data:image/jpg;base64," + res.data.data.qrcode;
            }
        },
        // 打开弹窗
        openDialog(row) {
            this.dialogVisible = true;
            if (row) {
                this.isEdit = true;
                this.formData = { ...row };
                console.log("33334------", row);

                // 设置图片数组
                this.headerImageArr = row.header_image
                    ? [row.header_image]
                    : [];
                this.displayImageArr = row.image ? [row.image] : [];
                this.shareImageArr = row.share_image ? [row.share_image] : [];
            } else {
                this.isEdit = false;
                this.resetForm();
            }
        },

        // 关闭弹窗
        closeDialog() {
            this.isEdit = false;
            this.resetForm();
        },

        // 重置表单
        resetForm() {
            this.formData = {
                id: null,
                activity_name: "",
                main_title: "",
                sub_title: "",
                custom_price: 0,
                custom_quantity: 1,
                sort_rule: 0,
                status: 1,
                sort_value: 0,
                header_image: "",
                image: "",
                share_image: "",
            };
            this.headerImageArr = [];
            this.displayImageArr = [];
            this.shareImageArr = [];
            this.$nextTick(() => {
                this.$refs.activityForm &&
                    this.$refs.activityForm.clearValidate();
            });
        },

        // 提交表单
        submitForm() {
            this.$refs.activityForm.validate((valid) => {
                if (valid) {
                    const submitData = {
                        activity_name: this.formData.activity_name,
                        main_title: this.formData.main_title,
                        sub_title: this.formData.sub_title,
                        custom_price: this.formData.custom_price,
                        custom_quantity: this.formData.custom_quantity,
                        sort_rule: this.formData.sort_rule,
                        status: this.formData.status,
                        sort_value: this.formData.sort_value,
                        header_image: this.headerImageArr.join(","),
                        image: this.displayImageArr.join(","),
                        share_image: this.shareImageArr.join(","),
                    };

                    // 如果是编辑，添加id
                    if (this.isEdit) {
                        submitData.id = this.formData.id;
                    }

                    const apiMethod = this.isEdit
                        ? "updateCustomActivity"
                        : "addCustomActivity";

                    this.$request.customActivity[apiMethod](submitData)
                        .then((result) => {
                            if (result.data.error_code == 0) {
                                this.$message.success("操作成功");
                                this.dialogVisible = false;
                                this.getActivityList();
                            } else {
                                this.$message.error(
                                    result.data.error_msg || "操作失败"
                                );
                            }
                        })
                        .catch((error) => {
                            console.error("提交失败:", error);
                            this.$message.error("操作失败");
                        });
                }
            });
        },

        // 获取排序标签
        getSortLabel(sort) {
            const sortLabels = {
                0: "添加时间倒序",
                1: "上架时间倒序",
                2: "闪购排序倒序",
                3: "随机",
            };
            return sortLabels[sort] || "";
        },

        // 分页相关
        handleSizeChange(limit) {
            this.queryForm.limit = limit;
            this.queryForm.page = 1;
            this.getActivityList();
        },

        handleCurrentChange(page) {
            this.queryForm.page = page;
            this.getActivityList();
        },

        // 商品列表相关方法
        // 打开商品列表弹窗
        openProductListDialog(activity) {
            this.currentActivity = activity;
            this.productListDialogVisible = true;
            this.getProductList(activity.id);
        },

        // 关闭商品列表弹窗
        closeProductListDialog() {
            this.productListDialogVisible = false;
            this.currentActivity = null;
            this.productList = [];
        },

        // 获取商品列表
        getProductList(activityId) {
            const params = {
                page: 1,
                limit: 100,
                activity_id: activityId,
            };

            // 调用API获取商品列表
            this.$request.customActivity
                .getCustomActivityGoodsList(params)
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.productList = result.data.data.list;
                    } else {
                        this.$message.error(
                            result.data.error_msg || "获取商品列表失败"
                        );
                    }
                })
                .catch((error) => {
                    console.error("获取商品列表失败:", error);
                    this.$message.error("获取商品列表失败");

                    // 如果API调用失败，使用模拟数据
                    this.productList = [
                        {
                            id: 9,
                            activity_id: activityId,
                            goods_name: "示例商品1",
                            period: 80878,
                            periods_type: 0,
                            package_id: 214834,
                            package_name: "单支",
                            package_price: 0.01,
                            product_copy: "示例商品文案1",
                            product_img: [
                                "https://images.wineyun.com/vinehoo/goods-images/20220524/1653363989893EDj57cNcH_产品图7.png?w=1920&h=1080",
                            ],
                            associated_products:
                                '[{"product_id":261,"nums":1,"isGift":0}]',
                            create_time: "2025-07-11 15:02:17",
                            update_time: "2025-07-11 15:02:17",
                        },
                    ];
                });
        },

        // 新增商品相关方法
        // 打开新增商品弹窗
        openAddProductDialog() {
            this.addProductDialogVisible = true;
            this.resetProductForm();
        },

        // 关闭新增商品弹窗
        closeAddProductDialog() {
            this.addProductDialogVisible = false;
            this.resetProductForm();
        },

        // 重置商品表单
        resetProductForm() {
            this.productFormData = {
                id: null,
                activity_id: this.currentActivity
                    ? this.currentActivity.id
                    : null,
                period: null,
                periods_type: 0,
                package_id: null,
                goods_name: "",
                product_copy: "",
            };
            this.packageOptions = [];
            this.selectedPackage = null;
            this.$nextTick(() => {
                this.$refs.productForm &&
                    this.$refs.productForm.clearValidate();
                // 清空富文本编辑器内容
                if (this.$refs.productCopyEditor) {
                    this.$refs.productCopyEditor.setContent("");
                }
            });
        },

        // 提交商品表单
        submitProductForm() {
            this.$refs.productForm.validate((valid) => {
                if (valid) {
                    const submitData = {
                        activity_id: this.currentActivity.id,
                        period: Number(this.productFormData.period),
                        periods_type: this.productFormData.periods_type,
                        package_id: this.productFormData.package_id,
                        goods_name: this.productFormData.goods_name,
                        product_copy: this.productFormData.product_copy,
                    };

                    // 调用API创建商品
                    this.$request.customActivity
                        .createCustomActivityGoods(submitData)
                        .then((result) => {
                            if (result.data.error_code == 0) {
                                this.$message.success("创建商品成功");
                                this.addProductDialogVisible = false;
                                this.getProductList(this.currentActivity.id);
                            } else {
                                this.$message.error(
                                    result.data.error_msg || "创建商品失败"
                                );
                            }
                        })
                        .catch((error) => {
                            console.error("创建商品失败:", error);
                            this.$message.error("创建商品失败");
                        });
                }
            });
        },

        queryPackages() {
            if (!this.productFormData.period) {
                this.$message.warning("请先输入商品期数");
                return;
            }

            // 清空之前的选择和输入
            this.productFormData.package_id = null;
            this.productFormData.goods_name = "";
            this.productFormData.product_copy = "";
            this.productFormData.periods_type = 0;
            this.selectedPackage = null;
            this.packageOptions = []; // 先清空套餐选项，避免旧数据闪现

            this.$request.customActivity
                .getCustomActivityPackage({
                    period_id: this.productFormData.period,
                })
                .then((result) => {
                    if (result.data.error_code == 0) {
                        this.packageOptions = result.data.data.list;
                        this.packageInfo = result.data.data.period_info;
                        this.productFormData.goods_name =
                            this.packageInfo.title;

                        // 将查询到的富文本内容显示在商品文案的富文本组件中
                        if (
                            this.packageInfo.detail &&
                            this.$refs.productCopyEditor
                        ) {
                            this.$nextTick(() => {
                                this.$refs.productCopyEditor.setContent(
                                    this.packageInfo.detail
                                );
                                this.productFormData.product_copy =
                                    this.packageInfo.detail;
                            });
                        }

                        if (this.packageOptions.length === 0) {
                            this.$message.warning("该期数暂无可用套餐");
                        } else {
                            this.$message.success(
                                `查询到 ${this.packageOptions.length} 个套餐`
                            );
                        }
                    } else {
                        this.packageOptions = [];
                        this.packageInfo = {};
                    }
                })
                .catch((error) => {
                    console.error("查询套餐失败:", error);
                    this.$message.error("查询套餐失败");
                    this.packageOptions = [];
                });
        },
        onPackageChange(packageId) {
            const selectedPkg = this.packageOptions.find(
                (pkg) => pkg.id === packageId
            );
            if (selectedPkg) {
                this.selectedPackage = selectedPkg;
                // 从套餐中获取频道类型
                this.productFormData.periods_type = selectedPkg.periods_type;
                // 自动填充商品名称（如果需要）
            }
        },
    },
};
</script>

<style scoped>
.dialog-footer {
    text-align: center;
}
</style>
